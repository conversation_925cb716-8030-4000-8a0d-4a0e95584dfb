<template>
  <ContentWrap>
    <div class="h-[calc(100vh-130px)]">
      <vxe-toolbar size="mini" custom ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" plain @click="searchWordFormRef?.openForm()">
            新增
          </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)'
          }"
          border
          align="center"
        >
          <vxe-column type="checkbox" width="50" />
          <vxe-column title="查询词" align="left" min-width="240" />
          <vxe-column title="开始时间" width="120" />
          <vxe-column title="结束时间" width="120" />
          <vxe-column title="审批状态" width="120" />
          <vxe-column title="操作" width="120" />
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <SearchWordForm ref="searchWordFormRef" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { SearchWordApi } from '@/api/patent/search-word';
import SearchWordForm from './SearchWordForm.vue'

const searchWordFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const queryParams = ref({
  pageNo: 1,
  pageSize: 30
})

const getList = async () => {}

onMounted(async () => {
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
