<template>
  <ContentWrap>
    <div class="h-[calc(100vh-160px)]">
      <vxe-toolbar size="mini" custom ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" @click="searchWordFormRef?.openForm()"> 新增 </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-60px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)'
          }"
          border
          align="center"
          :data="searchWordList"
          stripe
          :checkbox-config="{ highlight: true, range: true }"
        >
          <vxe-column type="checkbox" width="50" />
          <vxe-column title="查询词" field="word" align="left" min-width="240">
            <template #default="{ row }">
              <el-button type="primary" link @click="toBpm(row.processInstanceId)">{{
                row.word
              }}</el-button>
            </template>
          </vxe-column>
          <vxe-column title="开始时间" field="startDate" width="120">
            <template #default="{ row }">
              {{ formatToDate(row.startDate) }}
            </template>
          </vxe-column>
          <vxe-column title="结束时间" field="endDate" width="120">
            <template #default="{ row }">
              {{ formatToDate(row.endDate) }}
            </template>
          </vxe-column>
          <vxe-column title="审批状态" field="approvalStatus" width="120">
            <template #default="{ row }">
              <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
            </template>
          </vxe-column>
          <vxe-column title="操作" width="140">
            <template #default="{ row }">
              <el-button type="primary" link @click="row">编辑</el-button>
              <el-dropdown>
                <el-button type="warning" link><Icon icon="ep:d-arrow-right" /> 更多</el-button>
                <template #dropdown></template>
              </el-dropdown>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <SearchWordForm ref="searchWordFormRef" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { SearchWordApi } from '@/api/patent/search-word'
import SearchWordForm from './SearchWordForm.vue'
import { formatToDate } from '@/utils/dateUtil'

const searchWordFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const searchWordList = ref<any[]>([])
const loading = ref(false)
const queryParams = ref({
  pageNo: 1,
  pageSize: 30
})

const router = useRouter()

const getList = async () => {
  loading.value = true
  try {
    const res = await SearchWordApi.getSearchWordPage(queryParams.value)
    searchWordList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

onMounted(async () => {
  getList()
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
