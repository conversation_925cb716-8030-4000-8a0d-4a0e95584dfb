<template>
  <ContentWrap>
    <div class="h-[calc(100vh-130px)]">
      <vxe-toolbar size="mini" custom ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" plain @click="miningLayoutFormRef?.openForm()">
            新增
          </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)'
          }"
          :data="list"
          border
          align="center"
        >
          <vxe-column type="checkbox" width="50" />
          <vxe-column title="创新点/机会点" field="opportunityPoints" min-width="120">
            <template #header>
              <div>创新点/机会点</div>
              <el-input
                v-model="queryParams.opportunityPoints"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="提出人" field="proposer" min-width="120">
            <template #header>
              <div>提出人</div>
              <el-input
                v-model="queryParams.proposer"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>

          <vxe-column title="来源" field="source" min-width="120" />

          <vxe-column title="可应用的型号" field="models" min-width="120">
            <template #header>
              <div>可应用的型号</div>
              <el-input
                v-model="queryParams.models"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>

          <vxe-column title="拓展的专利点" field="patentPoint" min-width="120" />
          <vxe-column title="专利布局" field="patentLayout" min-width="120" />
          <vxe-column title="保护整机类型" field="machineType" min-width="120" />
          <vxe-column title="组件" field="component" min-width="120" />
          <vxe-column title="零件" field="part" min-width="120" />
          <vxe-column title="专利保护的地区" field="area" min-width="120" />
          <vxe-column title="申请号" field="applicationNo" min-width="120" />
          <vxe-column title="保护点" field="protectionPoint" min-width="120" />
          <vxe-column title="附件" field="attachmentIds" min-width="120" />
          <vxe-column title="发明人" field="inventor" min-width="120" />
          <vxe-column title="实际发明人" field="actualInventor" min-width="120" />
          <vxe-column title="创建时间" min-width="150" field="createTime" :formatter="dateFormatter3" />
          <vxe-column title="操作" min-width="100" fixed="right">
            <template #default="{ row }">
                <el-button
                  @click="miningLayoutFormRef?.openForm(row.id)"
                  link
                  type="primary"
                  v-hasPermi="['patent:mining-layout:update']"
                  >修改</el-button
                >
                <el-button
                  @click="handleDelete(row.id)"
                  link
                  type="danger"
                  v-hasPermi="['patent:mining-layout:delete']"
                  >删除</el-button
                >
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <MiningLayoutForm ref="miningLayoutFormRef"  @success="getList()" />
  </ContentWrap>
</template>
<script setup lang="ts">
import { MiningLayoutApi, MiningLayoutVO } from '@/api/patent/miningLayout';
import MiningLayoutForm from './MiningLayoutForm.vue';
import { ref, onMounted, nextTick } from 'vue'
import { dateFormatter3 } from '@/utils/formatTime'

// 消息弹窗
const message = useMessage() 
const miningLayoutFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const list = ref<MiningLayoutVO[]>([]) // 列表的数据
const loading = ref(true) // 列表的加载中
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  opportunityPoints: undefined,
  proposer: undefined,
  models: undefined,
})
// 筛选处理
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}
const getList = async () => {
  loading.value=true
  try{
    const data = await MiningLayoutApi.getMiningLayoutPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  }finally {
    loading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MiningLayoutApi.deleteMiningLayout(id)
    message.success('删除成功')
    await getList()
}


onMounted(async () => {
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>