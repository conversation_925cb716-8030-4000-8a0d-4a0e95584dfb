<template>
  <ContentWrap>
    <div class="h-[calc(100vh-130px)]">
      <vxe-toolbar size="mini" custom ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" plain @click="intelligenceFormRef?.openForm()">
            新增
          </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)'
          }"
          :data="list"
          border
          align="center"
        >
          <vxe-column type="checkbox" width="50" />
          <vxe-column title="专利号" field="patentNo" min-width="120">
            <template #header>
              <div>专利号</div>
              <el-input
                v-model="queryParams.patentNo"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="专利名称" field="patentName" min-width="120">
            <template #header>
              <div>专利名称</div>
              <el-input
                v-model="queryParams.patentName"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="我司关注的技术点" field="follow" min-width="600">
            <template #header>
              <div>我司关注的技术点</div>
              <el-input
                v-model="queryParams.follow"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="操作" min-width="100" fixed="right">
            <template #default="{ row }">
                <el-button
                  @click="intelligenceFormRef?.openForm(row.id)"
                  link
                  type="primary"
                  v-hasPermi="['patent:database:update']"
                  >修改</el-button
                >
                <el-button
                  @click="handleDelete(row.id)"
                  link
                  type="danger"
                  v-hasPermi="['patent:database:delete']"
                  >删除</el-button
                >
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <IntelligenceForm ref="intelligenceFormRef"  @success="getList()" />
  </ContentWrap>
</template>
<script setup lang="ts">
import { IntelligenceApi, IntelligenceVO } from '@/api/patent/intelligence';
import IntelligenceForm from './IntelligenceForm.vue';
import { ref, onMounted, nextTick } from 'vue'

// 消息弹窗
const message = useMessage() 
const intelligenceFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const list = ref<IntelligenceVO[]>([]) // 列表的数据
const loading = ref(true) // 列表的加载中
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  patentNo: undefined,
  patentName: undefined,
  follow: undefined,
})
// 筛选处理
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}
const getList = async () => {
  loading.value=true
  try{
    const data = await IntelligenceApi.getIntelligencePage(queryParams.value)
    list.value = data.list
    total.value = data.total
  }finally {
    loading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await IntelligenceApi.deleteIntelligence(id)
    message.success('删除成功')
    await getList()
}


onMounted(async () => {
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>