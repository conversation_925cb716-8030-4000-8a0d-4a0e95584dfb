<template>
  <div class="tags-input-container">
    <!-- 标签展示 -->
    <div class="tag-list">
      <span v-for="(tag, index) in localTags" :key="index" class="tag">
        {{ tag }}
        <el-button
          type="danger"
          size="small"
          :icon="Delete"
          circle
          @click="removeTag(index)"
        />
      </span>
    </div>

    <!-- 输入框 -->
    <input
      v-model="inputValue"
      @keyup.enter="addTag"
      placeholder="请输入内容后按回车"
      class="tag-input"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const localTags = ref([...props.modelValue])
const inputValue = ref('')
// 监听外部传入的值变化
watch(() => props.modelValue, (newVal) => {
  localTags.value = [...newVal]
})
// 添加标签
const addTag = () => {
  const value = inputValue.value.trim()
  if (value && !localTags.value.includes(value)) {
    localTags.value.push(value)
    inputValue.value = ''
    emit('update:modelValue', [...localTags.value])
  }
}

// 删除标签
const removeTag = (index: number) => {
  localTags.value.splice(index, 1)
  emit('update:modelValue', [...localTags.value])
}
</script>

<style lang="scss" scoped>
.tags-input-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 36px;

  .tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-right: 8px;
  }

  .tag {
    background-color: #f0f2f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #e6e8eb;
    }

    .el-icon-close {
      font-size: 12px;
    }
  }

  .tag-input {
    border: none;
    outline: none;
    flex: 1;
    padding: 4px 0;
    font-size: 13px;
  }
}
</style>
