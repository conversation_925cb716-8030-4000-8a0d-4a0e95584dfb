<template>
  <!-- 对话框(添加 / 修改) -->
  <el-drawer :title="dialogTitle" v-model="visible" :size="'50%'">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="100px"
      style="margin-top: 20px;"
    >
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="原申请人" prop="originalApplicant">
            <el-input v-model="formData.originalApplicant" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="当前权力人" prop="ownership">
            <el-input v-model="formData.ownership" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请号" prop="applicationNo">
            <el-input v-model="formData.applicationNo" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="公开号" prop="publicNo">
            <el-input v-model="formData.publicNo" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专利名称" prop="patentName">
            <el-input v-model="formData.patentName" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保护技术点" prop="protectionPoint">
            <el-input v-model="formData.protectionPoint" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="附件IDs" prop="attachmentIds">
            <UploadFile
              v-model="formData.attachmentIds"
              :limit="1"
              :file-size="100"
              class="min-w-80px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保护的产品型号" prop="models">
            <TagsInput v-model="formData.models" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专利类型" prop="patentType">
            <el-select
              v-model="formData.patentType"
              placeholder="请选择法律状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="申请日期" prop="applicationDate">
            <el-date-picker
              v-model="formData.applicationDate"
              value-format="YYYY-MM-DD"
              placeholder="申请日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="授权日期" prop="authorizationDate">
            <el-date-picker
              v-model="formData.authorizationDate"
              value-format="YYYY-MM-DD"
              placeholder="申请日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="有效期(年数)" prop="validityPeriod">
            <el-input v-model="formData.validityPeriod" type="number" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="发明人" prop="inventor">
            <el-input v-model="formData.inventor" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实际发明人" prop="actualInventor">
            <el-input v-model="formData.actualInventor" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="获取方式" prop="acquisitionMethod">
            <el-input v-model="formData.acquisitionMethod" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="法律状态" prop="legalStatus">
            <el-select
              v-model="formData.legalStatus"
              placeholder="请选择法律状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.LEGAL_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="代理机构" prop="agency">
            <el-input v-model="formData.agency" placeholder="请输入代理机构" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="同族专利" prop="families">
            <el-input v-model="formData.families" placeholder="请输入同族专利" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="24">
          <el-form-item label="维保评估" prop="maintenanceAssessment">
             <el-input
              v-model="formData.maintenanceAssessment"
              type="textarea"
              :rows="5"
              placeholder="请输入维保评估"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { MaintenanceApi } from '@/api/patent/maintenance'
import UploadFile from '@/components/UploadFile/src/UploadFile.vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import TagsInput from '../components/TagsInput.vue'
const visible = ref(false)
const formData = ref({
  id: undefined,
  originalApplicant: undefined,
  ownership: undefined,
  applicationNo: undefined,
  publicNo:  undefined,
  patentName:  undefined,
  protectionPoint: undefined,
  attachmentIds:  [] as string[],
  models:  [] as string[],
  patentType: undefined,
  applicationDate: undefined,
  authorizationDate: undefined,
  validityPeriod: undefined,
  inventor: undefined,
  actualInventor: undefined,
  acquisitionMethod: undefined,
  legalStatus: 0,
  agency: undefined,
  families:undefined,
  maintenanceAssessment: undefined
})

const formRules = reactive({
  applicationNo: [{ required: true, message: '请输入申请号', trigger: 'blur' }],
  patentName: [{ required: true, message: '请输入专利名称', trigger: 'blur' }],
  patentType: [{ required: true, message: '请选择专利类型', trigger: 'blur' }],
  protectionPoint: [{ required: true, message: '请输入保护点', trigger: 'blur' }],
})


// 抽屉的标题
const dialogTitle = ref('')
const formType = ref('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()

const databaseDialog = ref(false)

const openForm = async(rows?: any[] | any) => {
  visible.value = true
  if (rows) {
    formType.value = 'update'
    dialogTitle.value = '修改专利维保'
    const data = await MaintenanceApi.getMaintenance(rows)
    formData.value = {
      ...data,
      applicationDate: formatDate(data.applicationDate,"YYYY-MM-DD"),
      authorizationDate: formatDate(data.authorizationDate,"YYYY-MM-DD"),
    }
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加专利维保'
    resetForm()
  }
}
//重置表单
const resetForm = () => {
  formData.value = {
  id: undefined,
  originalApplicant: undefined,
  ownership: undefined,
  applicationNo: undefined,
  publicNo:  undefined,
  patentName:  undefined,
  protectionPoint: undefined,
  attachmentIds:  [] as string[],
  models:  [] as string[],
  patentType: undefined,
  applicationDate: undefined,
  authorizationDate: undefined,
  validityPeriod: undefined,
  inventor: undefined,
  actualInventor: undefined,
  acquisitionMethod: undefined,
  legalStatus: 0,
  agency: undefined,
  families:undefined,
  maintenanceAssessment: undefined
  }
}
const emit = defineEmits(['success']) 
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  console.log(formData.value)
  try {
    if (formType.value === 'update') {
      await MaintenanceApi.updateMaintenance(formData.value)
      message.success('修改成功')
    } else {
      await MaintenanceApi.createMaintenance(formData.value)
      message.success('创建成功')
    }
    visible.value = false
      emit('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>
