<template>
  <ContentWrap>
    <div class="h-[calc(100vh-130px)]">
      <vxe-toolbar size="mini" custom ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" plain @click="infringementFormRef?.openForm()">
            新增
          </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)'
          }"
          :data="list"
          border
          align="center"
        >
          <vxe-column type="checkbox" width="50" />
          <vxe-column title="提出人" field="submittedPeople" min-width="100">
            <template #header>
              <div>提出人</div>
              <el-input
                v-model="queryParams.submittedPeople"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="客户" field="customers" min-width="100">
            <template #header>
              <div>客户</div>
              <el-input
                v-model="queryParams.customers"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="产品型号" field="productModel" min-width="150">
            <template #header>
              <div>产品型号</div>
              <el-input
                v-model="queryParams.productModel"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="目标专利" field="targetPatent" min-width="120">
            <template #header>
              <div>目标专利</div>
              <el-input
                v-model="queryParams.targetPatent"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="目标专利基本信息" field="targetPatentInformation" min-width="150">
            <template #header>
              <div>目标专利基本信息</div>
              <el-input
                v-model="queryParams.targetPatentInformation"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="目标专利保护技术点" min-width="150" field="targetPatentPoints" />
          <vxe-column title="目标专利附图" min-width="120" field="targetPatentDrawings" />
          <vxe-column title="侵权分析报告（文本）" min-width="150" field="analysisReport" />
          <vxe-column title="侵权分析报告（附件）" min-width="150" field="analysisReportAttachment" />
          <vxe-column title="合理的规避方案" min-width="120" field="avoidanceScheme" />
          <!-- <vxe-column title="是否继续项目" min-width="120" field="isContinue" /> -->
          <vxe-column title="规避方案的专利申请与布局" min-width="200" field="avoidanceRemark" />
          <vxe-column title="交叉许可" min-width="120" field="crossLicensing" />
          <vxe-column title="规避方案跟进人" min-width="120" field="followPerson" />
          <vxe-column title="许可方式、费用及周期" min-width="160" field="costsCycles" />
          <vxe-column title="规避方案专利申请号" min-width="160" field="patentApplication" />
          <vxe-column title="创建时间" min-width="150" field="createTime" :formatter="dateFormatter3" />

          <vxe-column title="操作" min-width="100" fixed="right">
            <template #default="{ row }">
                <el-button
                  @click="infringementFormRef?.openForm(row.id)"
                  link
                  type="primary"
                  v-hasPermi="['patent:product-infringement:update']"
                  >修改</el-button
                >
                <el-button
                  @click="handleDelete(row.id)"
                  link
                  type="danger"
                  v-hasPermi="['patent:product-infringement:delete']"
                  >删除</el-button
                >
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <infringementForm ref="infringementFormRef"  @success="getList()" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { infringementApi, infringementVO } from '@/api/patent/infringement';
import infringementForm from './infringementForm.vue';
import { ref, onMounted, nextTick } from 'vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter3 } from '@/utils/formatTime'

// 消息弹窗
const message = useMessage() 
const infringementFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const list = ref<infringementVO[]>([]) // 列表的数据
const loading = ref(true) // 列表的加载中
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  submittedPeople: undefined,
  customers: undefined,
  productModel: undefined,
  targetPatent: undefined,
  targetPatentInformation: undefined,
})
// 筛选处理
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}
const getList = async () => {
  loading.value=true
  try{
    const data = await infringementApi.getProductInfringementPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  }finally {
    loading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await infringementApi.deleteProductInfringement(id)
    message.success('删除成功')
    // 刷新列表并保持滚动位置
    await getList()
}


onMounted(async () => {
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
