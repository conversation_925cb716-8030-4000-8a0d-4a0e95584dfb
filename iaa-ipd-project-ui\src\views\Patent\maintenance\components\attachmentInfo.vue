<template>
  <div class="attachment-info">
    <!-- 申请文件表格 -->
    <div class="table-section">
      <div class="table-header">
        <span class="table-title">申请文件</span>
        <el-button
          v-if="!isViewMode"
          type="primary"
          size="small"
          @click="handleAddAttachment"
        >
          新增
        </el-button>
      </div>
      <el-table :data="attachmentList" border size="small" class="custom-table">
        <el-table-column prop="attachmentName" label="附件名称" min-width="200">
          <template #default="{ row }">
            <el-link type="primary" @click="handlePreview(row)">
              {{ row.attachmentName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="attachmentType" label="附件类型" min-width="120" />
        <el-table-column prop="attachmentUrl" label="附件地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="uploadTime" label="上传时间" min-width="150" />
        <el-table-column prop="uploadUser" label="上传人" min-width="100" />
        <el-table-column prop="fileSize" label="文件大小" min-width="100" />
        <el-table-column prop="status" label="在线状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === '在线' ? 'success' : 'danger'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="!isViewMode" label="操作" width="150" align="center">
          <template #default="{ row, $index }">
            <el-button type="primary" link size="small" @click="handleEditAttachment(row, $index)">
              修改
            </el-button>
            <el-button type="danger" link size="small" @click="handleDeleteAttachment($index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 附件编辑对话框 -->
    <el-dialog
      v-model="attachmentDialogVisible"
      :title="attachmentDialogTitle"
      width="600px"
      @close="resetAttachmentForm"
    >
      <el-form
        ref="attachmentFormRef"
        :model="attachmentForm"
        :rules="attachmentRules"
        label-width="100px"
        size="small"
      >
        <el-form-item label="附件名称" prop="attachmentName">
          <el-input v-model="attachmentForm.attachmentName" />
        </el-form-item>
        <el-form-item label="附件类型" prop="attachmentType">
          <el-select v-model="attachmentForm.attachmentType" placeholder="请选择附件类型" style="width: 100%">
            <el-option label="说明书摘要" value="说明书摘要" />
            <el-option label="说明书" value="说明书" />
            <el-option label="权利要求书" value="权利要求书" />
            <el-option label="说明书附图" value="说明书附图" />
            <el-option label="摘要附图" value="摘要附图" />
            <el-option label="其他文件" value="其他文件" />
          </el-select>
        </el-form-item>
        <el-form-item label="文件上传" prop="attachmentUrl">
          <el-upload
            ref="uploadRef"
            :action="uploadAction"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :limit="1"
            :on-exceed="handleExceed"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传 PDF、DOC、DOCX、JPG、PNG 格式文件，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="attachmentForm.remark" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="attachmentDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAttachment">确定</el-button>
      </template>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="文件预览"
      width="80%"
      :before-close="handlePreviewClose"
    >
      <div class="preview-container">
        <iframe
          v-if="previewUrl && isPreviewable"
          :src="previewUrl"
          width="100%"
          height="600px"
          frameborder="0"
        ></iframe>
        <div v-else class="no-preview">
          <el-icon size="64"><Document /></el-icon>
          <p>该文件类型不支持预览</p>
          <el-button type="primary" @click="handleDownload">下载文件</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'

// Props
interface Props {
  mode?: 'view' | 'edit' | 'create'
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'edit'
})

// 是否为查看模式
const isViewMode = computed(() => props.mode === 'view')

// 上传配置
const uploadAction = '/api/upload' // 根据实际情况修改
const uploadHeaders = {
  'Authorization': 'Bearer ' + localStorage.getItem('token') || ''
}

// 附件数据
const attachmentList = ref([
  {
    attachmentName: '发明专利申请书—发明专利申请（用于提交PCT）',
    attachmentType: '说明书摘要',
    attachmentUrl: '/files/patent/application.pdf',
    uploadTime: '2017-10-01 15:41:12',
    uploadUser: '张三',
    fileSize: '成功提交',
    status: '在线'
  },
  {
    attachmentName: '说明书',
    attachmentType: '说明书',
    attachmentUrl: '/files/patent/description.pdf',
    uploadTime: '2017-10-01 15:41:12',
    uploadUser: '张三, 李四',
    fileSize: '',
    status: '在线'
  },
  {
    attachmentName: '权利要求书',
    attachmentType: '权利要求书',
    attachmentUrl: '/files/patent/claims.pdf',
    uploadTime: '2017-10-01 15:41:12',
    uploadUser: '张三, 李四',
    fileSize: '',
    status: '在线'
  },
  {
    attachmentName: '摘要附图',
    attachmentType: '摘要附图',
    attachmentUrl: '/files/patent/abstract.pdf',
    uploadTime: '2017-10-01 15:41:12',
    uploadUser: '张三, 李四',
    fileSize: '',
    status: '在线'
  },
  {
    attachmentName: '说明书附图',
    attachmentType: '说明书附图',
    attachmentUrl: '/files/patent/figures.pdf',
    uploadTime: '2017-10-01 15:41:12',
    uploadUser: '张三, 李四',
    fileSize: '',
    status: '在线'
  }
])

// 附件表单相关
const attachmentDialogVisible = ref(false)
const attachmentDialogTitle = ref('')
const attachmentFormRef = ref()
const uploadRef = ref()
const fileList = ref([])
const attachmentForm = reactive({
  attachmentName: '',
  attachmentType: '',
  attachmentUrl: '',
  remark: ''
})
const attachmentRules = {
  attachmentName: [{ required: true, message: '请输入附件名称', trigger: 'blur' }],
  attachmentType: [{ required: true, message: '请选择附件类型', trigger: 'change' }],
  attachmentUrl: [{ required: true, message: '请上传文件', trigger: 'blur' }]
}
let attachmentEditIndex = -1

// 预览相关
const previewDialogVisible = ref(false)
const previewUrl = ref('')
const isPreviewable = computed(() => {
  if (!previewUrl.value) return false
  const ext = previewUrl.value.split('.').pop()?.toLowerCase()
  return ['pdf', 'jpg', 'jpeg', 'png', 'gif'].includes(ext || '')
})

// 附件相关方法
const handleAddAttachment = () => {
  attachmentDialogTitle.value = '新增附件'
  resetAttachmentForm()
  attachmentEditIndex = -1
  attachmentDialogVisible.value = true
}

const handleEditAttachment = (row: any, index: number) => {
  attachmentDialogTitle.value = '编辑附件'
  Object.assign(attachmentForm, {
    attachmentName: row.attachmentName,
    attachmentType: row.attachmentType,
    attachmentUrl: row.attachmentUrl,
    remark: row.remark || ''
  })
  attachmentEditIndex = index
  attachmentDialogVisible.value = true
}

const handleDeleteAttachment = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个附件吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    attachmentList.value.splice(index, 1)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const resetAttachmentForm = () => {
  Object.assign(attachmentForm, {
    attachmentName: '',
    attachmentType: '',
    attachmentUrl: '',
    remark: ''
  })
  fileList.value = []
  attachmentFormRef.value?.clearValidate()
}

const saveAttachment = async () => {
  try {
    await attachmentFormRef.value?.validate()
    const newAttachment = {
      ...attachmentForm,
      uploadTime: new Date().toLocaleString(),
      uploadUser: '当前用户', // 实际应该从用户信息获取
      fileSize: '1.2MB', // 实际应该从文件信息获取
      status: '在线'
    }

    if (attachmentEditIndex >= 0) {
      // 编辑
      Object.assign(attachmentList.value[attachmentEditIndex], newAttachment)
      ElMessage.success('修改成功')
    } else {
      // 新增
      attachmentList.value.push(newAttachment)
      ElMessage.success('新增成功')
    }
    attachmentDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 文件上传相关方法
const beforeUpload = (file: File) => {
  const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png']
  const isAllowedType = allowedTypes.includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAllowedType) {
    ElMessage.error('只能上传 PDF、DOC、DOCX、JPG、PNG 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response: any, file: any) => {
  if (response.code === 200) {
    attachmentForm.attachmentUrl = response.data.url
    attachmentForm.attachmentName = attachmentForm.attachmentName || file.name
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

const handleUploadError = () => {
  ElMessage.error('文件上传失败')
}

const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

// 预览和下载相关方法
const handlePreview = (row: any) => {
  previewUrl.value = row.attachmentUrl
  previewDialogVisible.value = true
}

const handlePreviewClose = () => {
  previewUrl.value = ''
}

const handleDownload = () => {
  if (previewUrl.value) {
    const link = document.createElement('a')
    link.href = previewUrl.value
    link.download = ''
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 获取附件列表
const onListAttachment = async () => {
  // 可以在这里添加获取附件列表的逻辑
  return attachmentList.value
}

// 获取数据的方法
const getData = () => {
  return {
    attachmentList: attachmentList.value
  }
}

// 设置数据的方法
const setData = (data: any) => {
  if (data.attachmentList) {
    attachmentList.value = data.attachmentList
  }
}

// 暴露给父组件的方法
defineExpose({
  onListAttachment,
  getData,
  setData
})
</script>

<style lang="scss" scoped>
.attachment-info {
  .table-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .table-title {
        font-weight: bold;
        font-size: 14px;
        color: #333;

        &::before {
          content: '■';
          color: #409eff;
          margin-right: 5px;
        }
      }
    }

    .custom-table {
      :deep(.el-table__header) {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
        }
      }

      :deep(.el-table__body) {
        tr:hover > td {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

.preview-container {
  .no-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #909399;

    p {
      margin: 20px 0;
      font-size: 16px;
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 15px;
    border-top: 1px solid #e4e7ed;
  }
}

:deep(.el-upload) {
  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 7px;
  }
}
</style>