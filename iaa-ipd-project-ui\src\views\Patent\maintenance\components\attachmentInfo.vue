<template>
  <div class="attachment-info">
    <!-- 申请文件表格 -->
    <div class="table-section">
      <div class="table-header">
        <span class="table-title">申请文件</span>
          <el-button size="small" v-if="!isViewMode"  @click="fileUploadRef?.openDialog()">
              <img src="@/assets/opertion/upload.png" class="mr-10px w-20px h-20px" />
              上传文件
          </el-button>
      </div>
      <el-table :data="attachmentList" border size="small" class="custom-table">
        <el-table-column prop="attachmentName" label="附件名称" min-width="200">
          <template #default="{ row }">
            <el-link type="primary" @click="handlePreview(row)">
              {{ row.attachmentName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="attachmentUrl" label="附件地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="attachmentRemark" label="附件备注" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="上传时间" min-width="150" />
        <el-table-column v-if="!isViewMode" label="操作" width="150" align="center">
          <template #default="{ $index }">
            <el-button type="danger" link size="small" @click="handleDeleteAttachment($index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <FileUpload
    ref="fileUploadRef"
    v-model:openFolder="openFolder"
    @success="setAttachmentTableData"
  />
    <!-- 文件预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="文件预览"
      width="80%"
      :before-close="handlePreviewClose"
    >
      <div class="preview-container">
        <iframe
          v-if="previewUrl && isPreviewable"
          :src="previewUrl"
          width="100%"
          height="600px"
          frameborder="0"
        ></iframe>
        <div v-else class="no-preview">
          <el-icon size="64"><Document /></el-icon>
          <p>该文件类型不支持预览</p>
          <el-button type="primary" @click="handleDownload">下载文件</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { FileTemplateApi, FileTemplateVO } from '@/api/project/file/template'
import FileUpload from './MainFileUpload.vue'

// Props
interface Props {
  mode?: 'view' | 'edit' | 'create'
  initialData?: {
    attachmentList?: any[]
  }
}

const fileUploadRef = ref()
const openFolder = ref<FileTemplateVO | any>({} as any)
const props = withDefaults(defineProps<Props>(), {
  mode: 'edit',
  initialData: () => ({})
})

// 是否为查看模式
const isViewMode = computed(() => props.mode === 'view')

// 附件数据
const attachmentList = ref<any[]>([])

// 初始化数据
const initializeData = () => {
  if (props.initialData && props.initialData.attachmentList) {
    attachmentList.value = props.initialData.attachmentList
  }
}

// 清空数据
const clearData = () => {
  attachmentList.value = []
}

// 监听 initialData 变化
watch(() => props.initialData, (newData) => {
  if (newData && newData.attachmentList) {
    initializeData()
  } else {
    clearData()
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化数据
onMounted(() => {
  initializeData()
})

const setAttachmentTableData = (fileItemList: any[]) => {
  console.log(fileItemList)
  // attachmentList.value = fileUploadRef.value?.fileList
  attachmentList.value=fileItemList
}
// 附件表单相关
const attachmentDialogVisible = ref(false)
const attachmentDialogTitle = ref('')
const attachmentFormRef = ref()
const fileList = ref([])
const attachmentForm = reactive({
  attachmentName: '',
  attachmentType: '',
  attachmentUrl: '',
  remark: ''
})
let attachmentEditIndex = -1

// 预览相关
const previewDialogVisible = ref(false)
const previewUrl = ref('')
const isPreviewable = computed(() => {
  if (!previewUrl.value) return false
  const ext = previewUrl.value.split('.').pop()?.toLowerCase()
  return ['pdf', 'jpg', 'jpeg', 'png', 'gif'].includes(ext || '')
})

// 附件相关方法
const handleAddAttachment = () => {
  attachmentDialogTitle.value = '新增附件'
  resetAttachmentForm()
  attachmentEditIndex = -1
  attachmentDialogVisible.value = true
}

const handleEditAttachment = (row: any, index: number) => {
  attachmentDialogTitle.value = '编辑附件'
  Object.assign(attachmentForm, {
    attachmentName: row.attachmentName,
    attachmentType: row.attachmentType,
    attachmentUrl: row.attachmentUrl,
    remark: row.remark || ''
  })
  attachmentEditIndex = index
  attachmentDialogVisible.value = true
}

const handleDeleteAttachment = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个附件吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    attachmentList.value.splice(index, 1)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const resetAttachmentForm = () => {
  Object.assign(attachmentForm, {
    attachmentName: '',
    attachmentType: '',
    attachmentUrl: '',
    remark: ''
  })
  fileList.value = []
  attachmentFormRef.value?.clearValidate()
}

const saveAttachment = async () => {
  try {
    await attachmentFormRef.value?.validate()
    const newAttachment = {
      ...attachmentForm,
      uploadTime: new Date().toLocaleString(),
      uploadUser: '当前用户', // 实际应该从用户信息获取
      fileSize: '1.2MB', // 实际应该从文件信息获取
      status: '在线'
    }

    if (attachmentEditIndex >= 0) {
      // 编辑
      Object.assign(attachmentList.value[attachmentEditIndex], newAttachment)
      ElMessage.success('修改成功')
    } else {
      // 新增
      attachmentList.value.push(newAttachment)
      ElMessage.success('新增成功')
    }
    attachmentDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 预览和下载相关方法
const handlePreview = (row: any) => {
  previewUrl.value = row.attachmentUrl
  previewDialogVisible.value = true
}

const handlePreviewClose = () => {
  previewUrl.value = ''
}

const handleDownload = () => {
  if (previewUrl.value) {
    const link = document.createElement('a')
    link.href = previewUrl.value
    link.download = ''
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 获取附件列表
const onListAttachment = async () => {
  // 可以在这里添加获取附件列表的逻辑
  return attachmentList.value
}

// 获取数据的方法
const getData = () => {
  return {
    attachmentList: attachmentList.value
  }
}

// 设置数据的方法
const setData = (data: any) => {
  if (data.attachmentList) {
    attachmentList.value = data.attachmentList
  }
}

// 暴露给父组件的方法
defineExpose({
  onListAttachment,
  getData,
  setData
})
</script>

<style lang="scss" scoped>
.attachment-info {
  .table-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .table-title {
        font-weight: bold;
        font-size: 14px;
        color: #333;

        &::before {
          content: '■';
          color: #409eff;
          margin-right: 5px;
        }
      }
    }

    .custom-table {
      :deep(.el-table__header) {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
        }
      }

      :deep(.el-table__body) {
        tr:hover > td {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

.preview-container {
  .no-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #909399;

    p {
      margin: 20px 0;
      font-size: 16px;
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 15px;
    border-top: 1px solid #e4e7ed;
  }
}

:deep(.el-upload) {
  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 7px;
  }
}
</style>