<template>
  <!-- 对话框(添加 / 修改) -->
  <el-drawer :title="dialogTitle" v-model="visible" :size="'50%'">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="100px"
      style="margin-top: 20px"
    >
      <template v-if="typeOpertion === 0">
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="原申请人" prop="originalApplicant">
              <el-input v-model="formData.originalApplicant" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="当前权力人" prop="ownership">
              <el-input v-model="formData.ownership" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专利申请号" prop="applicationNo">
              <el-input v-model="formData.applicationNo" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="专利名称" prop="patentName">
              <TagsInput v-model="formData.patentName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保护技术点" prop="protectionPoint">
              <el-input v-model="formData.protectionPoint" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
          <el-form-item label="许可人" prop="licensorPeople">
            <el-input v-model="formData.licensorPeople" placeholder="请输入许可人或转让人" />
          </el-form-item>
        </el-col>
        </el-row>

        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="许可时间" prop="licensorDate">
              <el-date-picker
                v-model="formData.licensorDate"
                value-format="YYYY-MM-DD"
                placeholder="请选择许可或转让时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="费用" prop="costs">
              <el-input v-model="formData.costs" type="number" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合格通知书" prop="notice">
              <el-input v-model="formData.notice" />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-if="typeOpertion === 1">
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="申请人" prop="applicant">
              <el-input v-model="formData.applicant" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="被申请人" prop="beApplicant">
              <el-input v-model="formData.beApplicant" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="涉案专利号" prop="patentInvolved">
              <el-input v-model="formData.patentInvolved" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="涉案专利名称" prop="patentInvolvedName">
              <el-input v-model="formData.patentInvolvedName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="涉案专利保护点" prop="patentInvolvedPoint">
              <el-input v-model="formData.patentInvolvedPoint" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="涉及产品型号" prop="patentModel">
              <el-input v-model="formData.patentModel" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="诉讼案件来源" prop="litigationSource">
              <el-input v-model="formData.litigationSource" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="内部侵权分析报告" prop="analysisReport">
              <el-input v-model="formData.analysisReport" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="诉讼选择或可行性" prop="litigationSelect">
              <el-input v-model="formData.litigationSelect" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="诉讼或无效案件案号" prop="litigationCase">
              <el-input v-model="formData.litigationCase" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="委托机构及律师" prop="entrustedLawyer">
              <el-input v-model="formData.entrustedLawyer" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开庭时间" prop="trialDate">
              <el-date-picker
                v-model="formData.trialDate"
                value-format="YYYY-MM-DD"
                placeholder="申请日期"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="24">
            <el-form-item label="诉讼或无效结论" prop="litigationConclusion">
              <el-input
                v-model="formData.litigationConclusion"
                :rows="5"
                type="textarea"
                placeholder="请输入诉讼或无效结论"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { OperationApi } from '@/api/patent/operation'
import TagsInput from '../components/TagsInput.vue'

const typeOpertion = ref()
const visible = ref(false)
const formData = ref({
  id: undefined,
  originalType: typeOpertion.value,
  originalApplicant: undefined,
  ownership: undefined,
  applicationNo: undefined,
  publicNo: undefined,
  patentName: undefined,
  protectionPoint: undefined,
  licensorPeople: undefined,
  licensorDate: undefined,
  costs: undefined,
  notice: undefined,
  applicant: undefined,
  beApplicant: undefined,
  patentInvolved: undefined,
  patentInvolvedName: undefined,
  patentInvolvedPoint: undefined,
  patentModel: undefined,
  litigationSource: undefined,
  analysisReport: undefined,
  litigationSelect: undefined,
  litigationCase: undefined,
  entrustedLawyer: undefined,
  trialDate: undefined,
  litigationConclusion: undefined
})

const formRules = reactive({
  opportunityPoints: [{ required: true, message: '创新点或机会点', trigger: 'blur' }],
  proposer: [{ required: true, message: '请输入提出人', trigger: 'blur' }],
  applicationNo: [{ required: true, message: '请输入申请号', trigger: 'blur' }],
  models: [{ required: true, message: '请输入可应用的型号', trigger: 'blur' }],
  patentPoint: [{ required: true, message: '请输入可拓展的专利点', trigger: 'blur' }],
  area: [{ required: true, message: '请输入保护的地区', trigger: 'blur' }],
  protectionPoint: [{ required: true, message: '请输入保护点', trigger: 'blur' }]
})

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()

const openForm = async (type?: any, rows?: any[] | any) => {
  typeOpertion.value = type
  visible.value = true
  if (rows) {
    formType.value = 'update'
    dialogTitle.value = '修改知识产权运营'
    const data = await OperationApi.getOperation(rows)
    formData.value = {
      ...data
    }
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加知识产权运营'
    resetForm()
  }
}
//重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    originalType: typeOpertion.value,
    originalApplicant: undefined,
    ownership: undefined,
    applicationNo: undefined,
    publicNo: undefined,
    patentName: undefined,
    protectionPoint: undefined,
    licensorPeople: undefined,
    licensorDate: undefined,
    costs: undefined,
    notice: undefined,
    applicant: undefined,
    beApplicant: undefined,
    patentInvolved: undefined,
    patentInvolvedName: undefined,
    patentInvolvedPoint: undefined,
    patentModel: undefined,
    litigationSource: undefined,
    analysisReport: undefined,
    litigationSelect: undefined,
    litigationCase: undefined,
    entrustedLawyer: undefined,
    trialDate: undefined,
    litigationConclusion: undefined
  }
}
const emit = defineEmits(['success'])
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  console.log(formData.value)
  try {
    if (formType.value === 'update') {
      await OperationApi.updateOperation(formData.value)
      message.success('知识产权运营修改成功')
    } else {
      await OperationApi.createOperation(formData.value)
      message.success('知识产权运营创建成功')
    }
    visible.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>
