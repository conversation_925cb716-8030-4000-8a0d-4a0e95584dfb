<template>
  <!-- 对话框(添加 / 修改) -->
  <el-drawer :title="dialogTitle" v-model="visible" :size="'50%'">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="100px"
      style="margin-top: 20px;"
    >
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="产权搜索词" prop="word">
            <el-input v-model="formData.word" placeholder="请输入知识产权搜索词" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开始时间" prop="startDate">
            <el-date-picker
              v-model="formData.startDate"
              value-format="YYYY-MM-DD"
              placeholder="开始时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="结束时间" prop="endDate">
            <el-date-picker
              v-model="formData.endDate"
              value-format="YYYY-MM-DD"
              placeholder="结束时间"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="专利类型" prop="patentType">
            <el-select
              v-model="formData.patentType"
              placeholder="请选择法律状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专利号" prop="patentNo">
            <el-input v-model="formData.patentNo" placeholder="请输入专利号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专利名称" prop="patentName">
            <el-input v-model="formData.patentName" placeholder="请输入专利名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="公开号" prop="publicNo">
            <el-input v-model="formData.publicNo" placeholder="请输入公开号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="权利人" prop="ownership">
            <el-input v-model="formData.ownership" placeholder="请输入权利人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发明人" prop="inventor">
            <el-input v-model="formData.inventor" placeholder="请输入发明人" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="申请日期" prop="applicationDate">
            <el-date-picker
              v-model="formData.applicationDate"
              value-format="YYYY-MM-DD"
              placeholder="申请日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="授权日期" prop="authorizationDate">
            <el-date-picker
              v-model="formData.authorizationDate"
              value-format="YYYY-MM-DD"
              placeholder="授权日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="同族专利" prop="families">
            <el-input v-model="formData.families" placeholder="请输入同族专利" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="法律状态" prop="legalStatus">
            <el-select
              v-model="formData.legalStatus"
              placeholder="请选择法律状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.LEGAL_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="摘要" prop="abstracts">
            <el-input
              v-model="formData.abstracts"
              type="textarea"
              :rows="5"
              placeholder="请输入摘要"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="附件IDs" prop="attachmentIds">
            <UploadFile
              v-model="formData.attachmentIds"
              :limit="1"
              :file-size="100"
              class="min-w-80px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保护整机类型" prop="machineType">
              <tags-input v-model="formData.machineType" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="组件" prop="component">
            <el-input v-model="formData.component" placeholder="请输入组件" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="零件" prop="part">
            <el-input v-model="formData.part" placeholder="请输入零件" />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="功能点" prop="functionPoint">
            <el-input
              v-model="formData.functionPoint"
              type="textarea"
              :rows="5"
              placeholder="请输入功能点"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="技术点" prop="technologyPoint">
            <el-input v-model="formData.technologyPoint" placeholder="请输入技术点" />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="解决的问题" prop="solvedProblem">
            <el-input
              v-model="formData.solvedProblem"
              type="textarea"
              :rows="5"
              placeholder="请输入解决的问题"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ref } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { DatabaseApi } from '@/api/patent/database'
import UploadFile from '@/components/UploadFile/src/UploadFile.vue'
import { formatDate, parseDate } from '@/utils/formatTime'
import TagsInput from '../components/TagsInput.vue'
const visible = ref(false)
const formData = ref({
  id: undefined,
  word: undefined,
  startDate: undefined,
  endDate: undefined,
  patentNo: undefined,
  publicNo: undefined,
  patentName: undefined,
  patentType: undefined,
  ownership: undefined,
  inventor: undefined,
  applicationDate: undefined,
  authorizationDate: undefined,
  families: undefined,
  legalStatus: 0,
  abstracts: undefined,
  attachmentIds: [] as string[],
  machineType: [] as string[],
  component: undefined,
  part: undefined,
  technologyPoint: undefined,
  solvedProblem: undefined,
  functionPoint: undefined
})
const formRules = reactive({
  word: [{ required: true, message: '请输入搜索词', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择时间范围', trigger: 'change' }]
})

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()

const openForm = async(rows?: any[] | any) => {
  visible.value = true
  if (rows) {
    formType.value = 'update'
    dialogTitle.value = '修改知识产权'
    const data = await DatabaseApi.getDatabase(rows)
    // 时间字段转换：时间戳 -> YYYY-MM-DD 字符串
    formData.value = {
      ...data,
      startDate: formatDate(data.startDate,"YYYY-MM-DD"),
      endDate: formatDate(data.endDate,"YYYY-MM-DD"),
      applicationDate: formatDate(data.applicationDate,"YYYY-MM-DD"),
      authorizationDate: formatDate(data.authorizationDate,"YYYY-MM-DD")
    }
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加知识产权'
  }
}

const emit = defineEmits(['success']) 
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  console.log(formData.value)
  try {
    if (formType.value === 'update') {
      await DatabaseApi.updateDatabase(formData.value)
      message.success('修改成功')
    } else {
      await DatabaseApi.createDatabase(formData.value)
      message.success('创建成功')
    }
    visible.value = false
      emit('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>
