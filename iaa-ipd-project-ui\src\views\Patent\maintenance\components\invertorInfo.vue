<template>
  <div class="inventor-info">
    <!-- 发明人表格 -->
    <div class="table-section">
      <div class="table-header">
        <span class="table-title">发明人</span>
        <el-button
          v-if="!isViewMode"
          type="primary"
          size="small"
          @click="handleAddInventor"
        >
          新增
        </el-button>
      </div>
      <el-table :data="inventorList" border size="small" class="custom-table">
        <el-table-column type="index" label="序号" min-width="30" align="center" />
        <el-table-column prop="inventorName" label="姓名或名称" min-width="100" />
        <el-table-column prop="nameEnglish" label="姓名（英文）" min-width="100" />
        <el-table-column prop="nationality" label="国籍或地区" min-width="100" />
        <el-table-column prop="certificateType" label="证件类型" min-width="100">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.PATENT_CERTIFICATE_TYPE" :value="row.certificateType" />
          </template>
        </el-table-column>
        <el-table-column v-if="!isViewMode" label="操作" width="150" align="center">
          <template #default="{ row, $index }">
            <el-button type="primary" link size="small" @click="handleEditInventor(row, $index)">
              修改
            </el-button>
            <el-button type="danger" link size="small" @click="handleDeleteInventor($index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 申请人表格 -->
    <div class="table-section">
      <div class="table-header">
        <span class="table-title">申请人</span>
        <el-button
          v-if="!isViewMode"
          type="primary"
          size="small"
          @click="handleAddApplicant"
        >
          新增
        </el-button>
      </div>
      <el-table :data="applicationList" border size="small" class="custom-table">
        <el-table-column type="index" label="序号" min-width="30" align="center" />
        <el-table-column prop="applicationName" label="姓名或名称" min-width="80" />
        <el-table-column prop="applicationType" label="申请人类型" min-width="80" />
        <el-table-column prop="nationality" label="国籍" min-width="80" />
        <el-table-column prop="certificateType" label="证件类型" min-width="80">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.PATENT_CERTIFICATE_TYPE" :value="row.certificateType" />
          </template>
        </el-table-column>
        <el-table-column prop="address" label="详细地址" min-width="180" />
        <el-table-column v-if="!isViewMode" label="操作" width="150" align="center">
          <template #default="{ row, $index }">
            <el-button type="primary" link size="small" @click="handleEditApplicant(row, $index)">
              修改
            </el-button>
            <el-button type="danger" link size="small" @click="handleDeleteApplicant($index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 代理机构表格 -->
    <div class="table-section">
      <div class="table-header">
        <span class="table-title">代理机构</span>
        <el-button
          v-if="!isViewMode"
          type="primary"
          size="small"
          @click="handleAddAgency"
        >
          新增
        </el-button>
      </div>
      <el-table :data="agencyList" border size="small" class="custom-table">
        <el-table-column prop="agencyName" label="代理机构名称" min-width="150" />
        <el-table-column prop="agencyCode" label="代理机构代码" min-width="150" />
        <el-table-column prop="agencyAddress" label="代理机构地址" min-width="120" />
        <el-table-column prop="agencyPhone" label="代理机构电话" min-width="120" />
        <el-table-column v-if="!isViewMode" label="操作" width="150" align="center">
          <template #default="{ row, $index }">
            <el-button type="primary" link size="small" @click="handleEditAgency(row, $index)">
              修改
            </el-button>
            <el-button type="danger" link size="small" @click="handleDeleteAgency($index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 发明人编辑对话框 -->
    <el-dialog
      v-model="inventorDialogVisible"
      :title="inventorDialogTitle"
      width="30%"
      @close="resetInventorForm"
    >
      <el-form
        ref="inventorFormRef"
        :model="inventorForm"
        :rules="inventorRules"
        size="small"
        label-width="100px"
        style="margin-top: 20px;"
      >
      <el-row :gutter="20">
        <el-col :span="12">
        <el-form-item label="姓名或名称" prop="inventorName">
          <el-input v-model="inventorForm.inventorName" />
        </el-form-item>
        </el-col>
        <el-col :span="12">
        <el-form-item label="姓名（英文）" prop="nameEnglish">
          <el-input v-model="inventorForm.nameEnglish" />
        </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
        <el-form-item label="国籍或地区" prop="nationality">
          <el-input v-model="inventorForm.nationality" />
        </el-form-item>
        </el-col>
        <el-col :span="12">
        <el-form-item label="证件类型" prop="certificateType">
            <el-select
              v-model="inventorForm.certificateType"
              placeholder="请选择证件类型"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_CERTIFICATE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
        </el-form-item>
        </el-col>
      </el-row>
      </el-form>
      <template #footer>
        <el-button @click="inventorDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveInventor">确定</el-button>
      </template>
    </el-dialog>

    <!-- 申请人编辑对话框 -->
    <el-dialog
      v-model="applicantDialogVisible"
      :title="applicantDialogTitle"
      width="600px"
      @close="resetApplicantForm"
    >
      <el-form
        ref="applicantFormRef"
        :model="applicantForm"
        :rules="applicantRules"
        label-width="120px"
        size="small"
      >
        <el-form-item label="姓名或名称" prop="applicationName">
          <el-input v-model="applicantForm.applicationName" />
        </el-form-item>
        <el-form-item label="申请人类型" prop="applicationType">
          <el-input v-model="applicantForm.applicationType" />
        </el-form-item>
        <el-form-item label="国籍" prop="nationality">
          <el-input v-model="applicantForm.nationality" />
        </el-form-item>
        <el-form-item label="证件类型" prop="certificateType">
            <el-select
              v-model="applicantForm.certificateType"
              placeholder="请选择证件类型"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_CERTIFICATE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="applicantForm.address" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="applicantDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveApplicant">确定</el-button>
      </template>
    </el-dialog>

    <!-- 代理机构编辑对话框 -->
    <el-dialog
      v-model="agencyDialogVisible"
      :title="agencyDialogTitle"
      width="500px"
      @close="resetAgencyForm"
    >
      <el-form
        ref="agencyFormRef"
        :model="agencyForm"
        :rules="agencyRules"
        label-width="120px"
        size="small"
      >
        <el-form-item label="代理机构名称" prop="agencyName">
          <el-input v-model="agencyForm.agencyName" />
        </el-form-item>
        <el-form-item label="代理机构代码" prop="agencyCode">
          <el-input v-model="agencyForm.agencyCode" />
        </el-form-item>
        <el-form-item label="代理机构地址" prop="agencyAddress">
          <el-input v-model="agencyForm.agencyAddress" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="代理机构电话" prop="agencyPhone">
          <el-input v-model="agencyForm.agencyPhone" type="number" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="agencyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAgency">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

// Props
interface Props {
  mode?: any
  initialData?: any;
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'edit'
})

// 是否为查看模式
const isViewMode = computed(() => props.mode === 'view')

// 发明人数据
const inventorList = ref<any[]>([])

// 申请人数据
const applicationList = ref<any[]>([])

// 代理机构数据
const agencyList = ref<any[]>([])

// 初始化数据
const initializeData = () => {
  if (props.initialData) {
    inventorList.value = props.initialData.inventorList || [];
    applicationList.value = props.initialData.applicationList || [];
    agencyList.value = props.initialData.agencyList || [];
  }
}

// 清空数据
const clearData = () => {
  inventorList.value = []
  applicationList.value = []
  agencyList.value = []
}

// 监听 initialData 变化
watch(() => props.initialData, (newData) => {
  if (newData) {
    initializeData()
  } else {
    clearData()
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化数据
onMounted(() => {
  initializeData()
})
// 发明人表单相关
const inventorDialogVisible = ref(false)
const inventorDialogTitle = ref('')
const inventorFormRef = ref()
const inventorForm = reactive({
  id: '',
  inventorName: '',
  nameEnglish: '',
  nationality: '',
  certificateType: '',
  isAnnounced:0, //是否公布姓名
  tableType:4,//所属表：0 查询词管理,1 专利数据库,2 专利情报,3 专利挖掘与布局,4 专利维保,5 专利运营,6 产品专利侵权分析
  belongsId:'',//关联的主表ID
})
const inventorRules = {
  inventorName: [{ required: true, message: '请输入姓名或名称', trigger: 'blur' }]
}
let inventorEditIndex = -1

// 申请人表单相关
const applicantDialogVisible = ref(false)
const applicantDialogTitle = ref('')
const applicantFormRef = ref()
const applicantForm = reactive({
  id: '',
  applicationName: '',
  applicationType: '',
  nationality: '',
  certificateType: '',
  address: '',
  tableType:4,//所属表：0 查询词管理,1 专利数据库,2 专利情报,3 专利挖掘与布局,4 专利维保,5 专利运营,6 产品专利侵权分析
  belongsId:'',//关联的主表ID
})
const applicantRules = {
  applicantName: [{ required: true, message: '请输入姓名或名称', trigger: 'blur' }],
}
let applicantEditIndex = -1

// 代理机构表单相关
const agencyDialogVisible = ref(false)
const agencyDialogTitle = ref('')
const agencyFormRef = ref()
const agencyForm = reactive({
  id: '',
  agencyName: '',
  agencyCode: '',
  agencyAddress: '',
  agencyPhone: '',
  tableType:4,//所属表：0 查询词管理,1 专利数据库,2 专利情报,3 专利挖掘与布局,4 专利维保,5 专利运营,6 产品专利侵权分析
  belongsId:'',//关联的主表ID
})
const agencyRules = {
  agencyName: [{ required: true, message: '请输入代理机构名称', trigger: 'blur' }]
}
let agencyEditIndex = -1

// 发明人相关方法
const handleAddInventor = () => {
  inventorDialogTitle.value = '新增发明人'
  resetInventorForm()
  inventorEditIndex = -1
  inventorDialogVisible.value = true
}

const handleEditInventor = (row: any, index: number) => {
  inventorDialogTitle.value = '编辑发明人'
  Object.assign(inventorForm, row)
  inventorEditIndex = index
  inventorDialogVisible.value = true
}

const handleDeleteInventor = (index: number) => {
  inventorList.value.splice(index, 1)
  ElMessage.success('删除成功')
}

const resetInventorForm = () => {
  Object.assign(inventorForm, {
    sequence: inventorList.value.length + 1,
    inventorName: '',
    nationality: '',
    nationalityZh: '',
    certificateType: ''
  })
  inventorFormRef.value?.clearValidate()
}

const saveInventor = async () => {
  try {
    await inventorFormRef.value?.validate()
    if (inventorEditIndex >= 0) {
      // 编辑
      inventorList.value[inventorEditIndex] = { ...inventorForm }
      ElMessage.success('修改成功')
    } else {
      // 新增
      inventorList.value.push({ ...inventorForm })
      ElMessage.success('新增成功')
    }
    inventorDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 申请人相关方法
const handleAddApplicant = () => {
  applicantDialogTitle.value = '新增申请人'
  resetApplicantForm()
  applicantEditIndex = -1
  applicantDialogVisible.value = true
}

const handleEditApplicant = (row: any, index: number) => {
  applicantDialogTitle.value = '编辑申请人'
  Object.assign(applicantForm, row)
  applicantEditIndex = index
  applicantDialogVisible.value = true
}

const handleDeleteApplicant = (index: number) => {
  applicationList.value.splice(index, 1)
  ElMessage.success('删除成功')
}

const resetApplicantForm = () => {
  Object.assign(applicantForm, {
    sequence: applicationList.value.length + 1,
    applicantName: '',
    applicantType: '',
    applicantCategory: '',
    nationalityCountry: '',
    residenceCountry: '',
    detailedAddress: ''
  })
  applicantFormRef.value?.clearValidate()
}

const saveApplicant = async () => {
  try {
    await applicantFormRef.value?.validate()
    if (applicantEditIndex >= 0) {
      // 编辑
      applicationList.value[applicantEditIndex] = { ...applicantForm }
      ElMessage.success('修改成功')
    } else {
      // 新增
      applicationList.value.push({ ...applicantForm })
      ElMessage.success('新增成功')
    }
    applicantDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 代理机构相关方法
const handleAddAgency = () => {
  agencyDialogTitle.value = '新增代理机构'
  resetAgencyForm()
  agencyEditIndex = -1
  agencyDialogVisible.value = true
}

const handleEditAgency = (row: any, index: number) => {
  agencyDialogTitle.value = '编辑代理机构'
  Object.assign(agencyForm, row)
  agencyEditIndex = index
  agencyDialogVisible.value = true
}

const handleDeleteAgency = (index: number) => {
  agencyList.value.splice(index, 1)
  ElMessage.success('删除成功')
}

const resetAgencyForm = () => {
  Object.assign(agencyForm, {
    agencyName: '',
    agencyCode: '',
    firstAgent: '',
    secondAgent: ''
  })
  agencyFormRef.value?.clearValidate()
}

const saveAgency = async () => {
  try {
    await agencyFormRef.value?.validate()
    if (agencyEditIndex >= 0) {
      // 编辑
      agencyList.value[agencyEditIndex] = { ...agencyForm }
      ElMessage.success('修改成功')
    } else {
      // 新增
      agencyList.value.push({ ...agencyForm })
      ElMessage.success('新增成功')
    }
    agencyDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 获取数据的方法
const getData = () => {
  return {
    inventorList: inventorList.value,
    applicationList: applicationList.value,
    agencyList: agencyList.value
  }
}

// 设置数据的方法
const setData = (data: any) => {
  if (data.inventorList) {
    inventorList.value = data.inventorList
  }
  if (data.applicationList) {
    applicationList.value = data.applicationList
  }
  if (data.agencyList) {
    agencyList.value = data.agencyList
  }
}

// 刷新数据的方法
const refreshComment = () => {
  // 可以在这里添加刷新逻辑
}

// 暴露给父组件的方法
defineExpose({
  refreshComment,
  getData,
  setData
})
</script>

<style lang="scss" scoped>
.inventor-info {
  .table-section {
    margin-bottom: 20px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .table-title {
        font-weight: bold;
        font-size: 14px;
        color: #333;

        &::before {
          content: '■';
          color: #409eff;
          margin-right: 5px;
        }
      }
    }

    .custom-table {
      :deep(.el-table__header) {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
        }
      }

      :deep(.el-table__body) {
        tr:hover > td {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    padding: 20px 40px;
  }

  .el-dialog__footer {
    padding: 10px 20px 15px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>